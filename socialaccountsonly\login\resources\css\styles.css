.login-pf body {
    background: #152935;
    background-size: cover;
    height: 100%;
}

/* Custom logo styling */
.login-pf-page .login-pf-brand {
    margin-bottom: 30px;
    text-align: center;
}

.login-pf-page .login-pf-brand img {
    max-width: 200px;
    height: auto;
}

.login-pf-page .login-pf-page-header {
    margin-bottom: 0px;
}

/* Alternative: Add logo above the login form */
#kc-header-wrapper::before {
    content: "";
    display: block;
    width: 300px;
    height: 80px;
    margin: 0 auto 15px auto;
    background-image: url('../img/MobileAspects-Logo.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

/* Style the header wrapper */
#kc-header-wrapper {
    text-align: center;
    margin-bottom: 20px;
}

/* Hide the username and password fields */
#kc-form-login {
    display: none;
}

#kc-social-providers {
  margin-top: 0 !important;
  padding-top: 0 !important;
  border-top: none !important;
  background: none !important;
}

/* Remove the visible line */
#kc-social-providers hr {
  display: none !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
}

/* Remove "Or sign in with" text */
#kc-social-providers h2 {
  display: none !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Increase Microsoft login button height by 20px */
#kc-social-providers .kc-social-provider-link[href*="microsoft"],
#kc-social-providers a[href*="microsoft"] {
  padding-top: calc(0.5em + 10px) !important;
  padding-bottom: calc(0.5em + 10px) !important;
}

/* Change Microsoft button logo to custom favicon */
#kc-social-providers .kc-social-provider-link[href*="microsoft"]::before,
#kc-social-providers a[href*="microsoft"]::before {
  content: "" !important;
  display: inline-block !important;
  width: 20px !important;
  height: 20px !important;
  background-image: url('../img/favicon.ico') !important;
  background-size: contain !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
  margin-right: 8px !important;
  vertical-align: middle !important;
}

/* Hide the default Microsoft icon if it exists */
#kc-social-providers .kc-social-provider-link[href*="microsoft"] img,
#kc-social-providers a[href*="microsoft"] img {
  display: none !important;
}

/* Ensure the button text is properly aligned with the new icon */
#kc-social-providers .kc-social-provider-link[href*="microsoft"],
#kc-social-providers a[href*="microsoft"] {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}
