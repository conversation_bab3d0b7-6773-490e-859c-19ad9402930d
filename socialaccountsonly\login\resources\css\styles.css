.login-pf body {
    background: #152935;
    background-size: cover;
    height: 100%;
}

/* Custom logo styling */
.login-pf-page .login-pf-brand {
    margin-bottom: 30px;
    text-align: center;
}

.login-pf-page .login-pf-brand img {
    max-width: 200px;
    height: auto;
}

.login-pf-page .login-pf-page-header {
    margin-bottom: 0px;
}

/* Alternative: Add logo above the login form */
#kc-header-wrapper::before {
    content: "";
    display: block;
    width: 300px;
    height: 80px;
    margin: 0 auto 15px auto;
    background-image: url('../img/MobileAspects-Logo.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

/* Style the header wrapper */
#kc-header-wrapper {
    text-align: center;
    margin-bottom: 20px;
}

/* Hide the username and password fields */
#kc-form-login {
    display: none;
}

#kc-social-providers {
  margin-top: 0 !important;
  padding-top: 0 !important;
  border-top: none !important;
  background: none !important;
}

/* Remove the visible line */
#kc-social-providers hr {
  display: none !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
}

/* Remove "Or sign in with" text */
#kc-social-providers h2 {
  display: none !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Make social account buttons bigger */
#kc-social-providers ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

#kc-social-providers li {
  margin-bottom: 15px;
}

#kc-social-providers .kc-social-provider-link,
#kc-social-providers a {
  display: block !important;
  width: 100% !important;
  min-height: 60px !important;
  padding: 15px 20px !important;
  font-size: 18px !important;
  font-weight: 500 !important;
  text-align: center !important;
  text-decoration: none !important;
  border-radius: 8px !important;
  transition: all 0.3s ease !important;
  box-sizing: border-box !important;
}

/* Hover effects for social buttons */
#kc-social-providers .kc-social-provider-link:hover,
#kc-social-providers a:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* Specific styling for different social providers */
#kc-social-providers .kc-social-provider-link[href*="google"],
#kc-social-providers a[href*="google"] {
  background-color: #4285f4 !important;
  color: white !important;
  border: 2px solid #4285f4 !important;
}

#kc-social-providers .kc-social-provider-link[href*="microsoft"],
#kc-social-providers a[href*="microsoft"] {
  background-color: #0078d4 !important;
  color: white !important;
  border: 2px solid #0078d4 !important;
}

#kc-social-providers .kc-social-provider-link[href*="facebook"],
#kc-social-providers a[href*="facebook"] {
  background-color: #1877f2 !important;
  color: white !important;
  border: 2px solid #1877f2 !important;
}

#kc-social-providers .kc-social-provider-link[href*="github"],
#kc-social-providers a[href*="github"] {
  background-color: #333 !important;
  color: white !important;
  border: 2px solid #333 !important;
}

/* Generic fallback for other social providers */
#kc-social-providers .kc-social-provider-link:not([href*="google"]):not([href*="microsoft"]):not([href*="facebook"]):not([href*="github"]),
#kc-social-providers a:not([href*="google"]):not([href*="microsoft"]):not([href*="facebook"]):not([href*="github"]) {
  background-color: #007bff !important;
  color: white !important;
  border: 2px solid #007bff !important;
}
