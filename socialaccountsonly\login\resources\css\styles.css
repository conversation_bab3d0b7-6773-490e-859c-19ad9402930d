.login-pf body {
    background: #152935;
    background-size: cover;
    height: 100%;
}

/* Custom logo styling */
.login-pf-page .login-pf-brand {
    margin-bottom: 30px;
    text-align: center;
}

.login-pf-page .login-pf-brand img {
    max-width: 200px;
    height: auto;
}

.login-pf-page .login-pf-page-header {
    margin-bottom: 0px;
}

/* Alternative: Add logo above the login form */
#kc-header-wrapper::before {
    content: "";
    display: block;
    width: 300px;
    height: 80px;
    margin: 0 auto 15px auto;
    background-image: url('../img/MobileAspects-Logo.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

/* Style the header wrapper */
#kc-header-wrapper {
    text-align: center;
    margin-bottom: 20px;
}

/* Hide the username and password fields */
#kc-form-login {
    display: none;
}

#kc-social-providers {
  margin-top: 0 !important;
  padding-top: 0 !important;
  border-top: none !important;
  background: none !important;
}

/* Remove the visible line */
#kc-social-providers hr {
  display: none !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
}

/* Remove "Or sign in with" text */
#kc-social-providers h2 {
  display: none !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Increase Microsoft login button height by 20px and add rounded corners */
#kc-social-providers .kc-social-provider-link[href*="microsoft"],
#kc-social-providers a[href*="microsoft"] {
  padding-top: calc(0.5em + 10px) !important;
  padding-bottom: calc(0.5em + 10px) !important;
}

/* Replace the Font Awesome Windows logo with custom image */
#kc-social-providers .kc-social-provider-link[href*="microsoft"] i,
#kc-social-providers a[href*="microsoft"] i,
#kc-social-providers .kc-social-provider-link[href*="microsoft"] .fa,
#kc-social-providers a[href*="microsoft"] .fa,
#kc-social-providers .kc-social-provider-link[href*="microsoft"] .fab,
#kc-social-providers a[href*="microsoft"] .fab,
#kc-social-providers .kc-social-provider-link[href*="microsoft"] .fas,
#kc-social-providers a[href*="microsoft"] .fas {
  display: inline-block !important;
  width: 20px !important;
  height: 20px !important;
  background-image: url('../img/maicon.png') !important;
  background-size: contain !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
  text-indent: -9999px !important;
  font-size: 0 !important;
  color: transparent !important;
  overflow: hidden !important;
}
